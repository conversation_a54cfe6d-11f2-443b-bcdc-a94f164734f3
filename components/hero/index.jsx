import React from "react";
import HeroBannerVideo from "./HeroBannerVideo";
import FeatureCards from "./FeatureCards";
import Wrapper from "@/components/Wrapper";
import TrustedBy from "../home/<USER>";
import Stats from "../home/<USER>";
import StatsDirectory from "../home/<USER>";
import TopSpecialties from "../home/<USER>";
import MedicalNetwork from "../home/<USER>";
import FeaturedHospitals from "../home/<USER>";
import FAQSection from "../home/<USER>";
import TopDoctors from "../home/<USER>";
import Testimonials from "../home/<USER>";
import Articles from "../home/<USER>";
import AppointmentRequest from "../home/<USER>";

const HeroSection = () => {
  return (
    <Wrapper padding="none">
      <div className="flex flex-col items-center w-full">
        <HeroBannerVideo />
        <div className="w-full px-4 md:px-6 lg:px-8 mt-8">
          <FeatureCards />
        </div>
        <TrustedBy />
        <Stats />
        <StatsDirectory />
        <TopSpecialties />
        <MedicalNetwork />
        <FeaturedHospitals />
        <FAQSection />
        <TopDoctors />
        <Testimonials />
        <Articles />
        <AppointmentRequest />
      </div>
    </Wrapper>
  );
};

export default HeroSection;
