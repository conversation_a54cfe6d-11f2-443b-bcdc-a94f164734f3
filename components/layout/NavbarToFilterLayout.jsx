import React from "react";
import Navbar from "@/components/navbar/Navbar";
import HeroInfo from "@/components/hero/HeroInfo";
import FilterBar from "@/components/hero/FilterBar";

const NavbarToFilterLayout = () => {
  return (
    <div
      className="w-full bg-cover bg-no-repeat bg-center rounded-t-2xl min-h-[600px] relative pb-20"
      style={{ backgroundImage: "url(/backgroundImg.png)" }}
    >
      {/* Content */}
      <div className="relative z-20">
        <Navbar />
        <HeroInfo />
        <FilterBar />
      </div>
    </div>
  );
};

export default NavbarToFilterLayout;
